//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/7/2.
//

#include <internal/circular_linked_list.h>
#include <internal/linked_list_traits.h>

typedef struct node_t {
  struct node_t *next;
  dsa_element_pt data;
} node_t;

typedef struct {
  trait_linked_list_t const *traits;
  node_t *head;
  size_t size;
} circular_linked_t;

static size_t circular_linked_size(dsa_const_container_pt list) {
  circular_linked_t const *this = list;
  return this ? this->size : SIZE_MAX;
}

static bool circular_linked_is_empty(dsa_const_container_pt list) {
  circular_linked_t const *this = list;
  if (!this) {
    return true;
  }
  return this->size == 0;
}

static dsa_result_t circular_linked_clear(dsa_container_pt list) {
  
  return DSA_SUCCESS;
}

dsa_linked_list_t *circular_linked_list_create() { return NULL; }